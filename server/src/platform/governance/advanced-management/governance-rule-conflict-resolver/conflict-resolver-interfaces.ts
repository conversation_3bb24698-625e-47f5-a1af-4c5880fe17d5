/**
 * @file Governance Rule Conflict Resolver Interfaces
 * @filepath server/src/platform/governance/advanced-management/governance-rule-conflict-resolver/conflict-resolver-interfaces.ts
 * @task-id G-TSK-CONFLICT-RESOLVER-INTERFACES
 * @component governance-rule-conflict-resolver-interfaces
 * @reference governance-context.CONFLICT.001
 * @template enterprise-governance-interfaces
 * @tier T1
 * @context governance-context
 * @category Advanced Management - Conflict Resolution
 * @created 2025-09-02
 * @modified 2025-09-02
 * 
 * @description
 * Enterprise-grade governance rule conflict resolver interfaces providing:
 * - Comprehensive conflict detection and resolution interfaces
 * - Advanced priority management and hierarchy interfaces
 * - Rule conflict analysis and resolution strategy interfaces
 * - Performance monitoring and metrics collection interfaces
 * - Memory-safe resource management interface definitions
 * - Resilient timing integration interface specifications
 * 
 * @compliance
 * - OA Framework Standards: Interface naming with 'I' prefix, type naming with 'T' prefix
 * - Anti-Simplification Policy: Complete enterprise functionality interfaces
 * - Memory Safety: Bounded collection interfaces, automatic cleanup specifications
 * - Performance: <2000ms conflict resolution operations for enterprise-scale systems
 * 
 * @security
 * - Input validation interfaces for all conflict specifications
 * - Resource exhaustion protection with memory boundary interfaces
 * - Secure conflict access with authorization check interfaces
 * - Conflict integrity validation with checksum interfaces
 * 
 * @performance
 * - Optimized conflict detection and resolution algorithm interfaces
 * - Efficient priority management and hierarchy caching interfaces
 * - Memory-bounded conflict storage interfaces
 * - Parallel conflict resolution processing interfaces
 * 
 * <AUTHOR> Consultancy - Advanced Governance Team
 * @version 1.0.0
 * @since 2025-09-02
 */

// ============================================================================
// IMPORTS AND DEPENDENCIES
// ============================================================================

// Core Framework Infrastructure
import { IGovernanceService } from '../../../../../shared/src/types/platform/governance/automation-processing-types';
import { IVersionManager } from '../version-manager-interfaces';
import { TValidationResult, TMetrics } from '../../../../../shared/src/types/platform/tracking/tracking-types';

// Governance Rule Types
import {
  TGovernanceRule,
  TGovernanceRuleSet,
  TGovernanceRuleType
} from '../../../../../shared/src/types/platform/governance/governance-types';

// ============================================================================
// CORE CONFLICT RESOLUTION INTERFACES
// ============================================================================

/**
 * Main Governance Rule Conflict Resolver Interface
 * Extends IGovernanceService and IVersionManager for complete functionality
 */
export interface IGovernanceRuleConflictResolver extends IGovernanceService, IVersionManager {
  // ============================================================================
  // RULE PRIORITY MANAGEMENT
  // ============================================================================
  
  /**
   * Manage rule priorities with comprehensive configuration
   * @param rules - Array of governance rules to manage
   * @param priorityConfig - Priority management configuration
   * @returns Promise resolving to priority management result
   */
  manageRulePriorities(
    rules: TGovernanceRule[], 
    priorityConfig: TRulePriorityConfig
  ): Promise<IPriorityManagementResult>;

  /**
   * Detect priority conflicts in rule set
   * @param ruleSet - Governance rule set to analyze
   * @returns Promise resolving to array of priority conflicts
   */
  detectPriorityConflicts(ruleSet: TGovernanceRuleSet): Promise<IPriorityConflict[]>;

  /**
   * Resolve priority conflicts using specified strategy
   * @param conflicts - Array of priority conflicts to resolve
   * @param strategy - Priority resolution strategy to apply
   * @returns Promise resolving to priority resolution result
   */
  resolvePriorityConflicts(
    conflicts: IPriorityConflict[], 
    strategy: TPriorityResolutionStrategy
  ): Promise<IPriorityResolutionResult>;

  /**
   * Validate priority hierarchy for rule set
   * @param ruleSet - Governance rule set to validate
   * @returns Promise resolving to priority validation result
   */
  validatePriorityHierarchy(ruleSet: TGovernanceRuleSet): Promise<IPriorityValidationResult>;

  /**
   * Get priority-based execution order for rules
   * @param ruleSet - Governance rule set to order
   * @returns Promise resolving to rule execution order
   */
  getPriorityExecutionOrder(ruleSet: TGovernanceRuleSet): Promise<TRuleExecutionOrder>;

  /**
   * Escalate rule priority with justification
   * @param ruleId - Rule identifier to escalate
   * @param escalationReason - Reason for priority escalation
   * @returns Promise resolving to priority escalation result
   */
  escalateRulePriority(ruleId: string, escalationReason: string): Promise<IPriorityEscalationResult>;

  // ============================================================================
  // CONFLICT DETECTION AND ANALYSIS
  // ============================================================================

  /**
   * Detect conflicts in governance rule set
   * @param ruleSet - Governance rule set to analyze
   * @param context - Conflict detection context
   * @returns Promise resolving to array of rule conflicts
   */
  detectConflicts(
    ruleSet: TGovernanceRuleSet, 
    context: TConflictDetectionContext
  ): Promise<IRuleConflict[]>;

  /**
   * Analyze detected conflicts with comprehensive assessment
   * @param conflicts - Array of rule conflicts to analyze
   * @param analysisConfig - Conflict analysis configuration
   * @returns Promise resolving to array of conflict analysis results
   */
  analyzeConflicts(
    conflicts: IRuleConflict[], 
    analysisConfig: TConflictAnalysisConfig
  ): Promise<IConflictAnalysisResult[]>;

  /**
   * Validate resolution strategies for conflicts
   * @param conflicts - Array of rule conflicts
   * @param strategies - Array of resolution strategies to validate
   * @returns Promise resolving to array of strategy validation results
   */
  validateResolutionStrategies(
    conflicts: IRuleConflict[], 
    strategies: TResolutionStrategy[]
  ): Promise<IStrategyValidationResult[]>;

  // ============================================================================
  // CONFLICT RESOLUTION OPERATIONS
  // ============================================================================

  /**
   * Resolve individual conflict using specified strategy
   * @param conflictId - Conflict identifier to resolve
   * @param strategy - Resolution strategy to apply
   * @param options - Optional conflict resolution options
   * @returns Promise resolving to conflict resolution result
   */
  resolveConflict(
    conflictId: string, 
    strategy: TResolutionStrategy, 
    options?: TConflictResolutionOptions
  ): Promise<IConflictResolution>;

  /**
   * Resolve multiple conflicts in batch operation
   * @param conflicts - Array of rule conflicts to resolve
   * @param batchConfig - Batch resolution configuration
   * @returns Promise resolving to batch resolution result
   */
  resolveConflictsBatch(
    conflicts: IRuleConflict[], 
    batchConfig: TBatchResolutionConfig
  ): Promise<IBatchResolutionResult>;

  /**
   * Auto-resolve conflicts using ML-based intelligent resolution
   * @param conflicts - Array of rule conflicts to auto-resolve
   * @param autoConfig - Auto-resolution configuration
   * @returns Promise resolving to auto-resolution result
   */
  autoResolveConflicts(
    conflicts: IRuleConflict[], 
    autoConfig: TAutoResolutionConfig
  ): Promise<IAutoResolutionResult>;

  // ============================================================================
  // CONFLICT PREVENTION AND LEARNING
  // ============================================================================

  /**
   * Generate prevention rules from conflict history
   * @param conflictHistory - Historical conflict data
   * @param preventionConfig - Prevention rule generation configuration
   * @returns Promise resolving to array of prevention rules
   */
  generatePreventionRules(
    conflictHistory: IConflictHistory[], 
    preventionConfig: TPreventionConfig
  ): Promise<IPreventionRule[]>;

  /**
   * Learn from resolution history to improve strategies
   * @param resolutionHistory - Historical resolution data
   * @param learningConfig - Learning algorithm configuration
   * @returns Promise resolving to learning result
   */
  learnFromResolutions(
    resolutionHistory: IConflictResolution[], 
    learningConfig: TLearningConfig
  ): Promise<ILearningResult>;

  /**
   * Predict potential conflicts before they occur
   * @param ruleSet - Governance rule set to analyze
   * @param predictionConfig - Conflict prediction configuration
   * @returns Promise resolving to array of conflict predictions
   */
  predictConflicts(
    ruleSet: TGovernanceRuleSet, 
    predictionConfig: TPredictionConfig
  ): Promise<IConflictPrediction[]>;

  // ============================================================================
  // MONITORING AND REPORTING
  // ============================================================================

  /**
   * Monitor resolution performance with real-time metrics
   * @param monitoringConfig - Performance monitoring configuration
   * @returns Promise resolving to resolution performance metrics
   */
  monitorResolutionPerformance(
    monitoringConfig: TMonitoringConfig
  ): Promise<IResolutionPerformanceMetrics>;

  /**
   * Generate comprehensive conflict reports
   * @param reportConfig - Conflict report configuration
   * @returns Promise resolving to conflict report
   */
  generateConflictReports(reportConfig: TConflictReportConfig): Promise<IConflictReport>;

  /**
   * Get conflict statistics for specified time range
   * @param timeRange - Time range for statistics
   * @returns Promise resolving to conflict statistics
   */
  getConflictStatistics(timeRange: TTimeRange): Promise<IConflictStatistics>;
}

// ============================================================================
// PRIORITY MANAGEMENT INTERFACES
// ============================================================================

/**
 * Priority Management Result Interface
 * Result of rule priority management operations
 */
export interface IPriorityManagementResult {
  /** Operation success status */
  success: boolean;
  
  /** Number of rules processed */
  rulesProcessed: number;
  
  /** Priority assignments made */
  priorityAssignments: Map<string, TRulePriority>;
  
  /** Conflicts detected during management */
  conflictsDetected: IPriorityConflict[];
  
  /** Warnings generated */
  warnings: string[];
  
  /** Operation timing information */
  timing: {
    startedAt: Date;
    completedAt: Date;
    durationMs: number;
  };
  
  /** Operation metadata */
  metadata: Record<string, unknown>;
}

/**
 * Priority Conflict Interface
 * Represents conflicts between rule priorities
 */
export interface IPriorityConflict {
  /** Unique conflict identifier */
  conflictId: string;
  
  /** Conflicting rule identifiers */
  conflictingRules: string[];
  
  /** Type of priority conflict */
  conflictType: 'duplicate-priority' | 'inheritance-conflict' | 'context-conflict' | 'cascade-conflict';
  
  /** Conflict severity level */
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  /** When conflict was detected */
  detectedAt: Date;
  
  /** Priority context information */
  context: TPriorityContext;
  
  /** Impact analysis of the conflict */
  impactAnalysis: {
    affectedRules: string[];
    executionImpact: string;
    performanceImpact: string;
    businessImpact: string;
  };
  
  /** Suggested resolution strategies */
  resolutionSuggestions: Array<{
    strategy: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
  }>;
}

/**
 * Priority Resolution Result Interface
 * Result of priority conflict resolution operations
 */
export interface IPriorityResolutionResult {
  /** Resolution success status */
  success: boolean;

  /** Number of conflicts resolved */
  conflictsResolved: number;

  /** Resolution strategy used */
  strategyUsed: TPriorityResolutionStrategy;

  /** Updated rule priorities */
  updatedPriorities: Map<string, TRulePriority>;

  /** Remaining unresolved conflicts */
  unresolvedConflicts: IPriorityConflict[];

  /** Resolution outcome details */
  outcome: {
    priorityChanges: Array<{
      ruleId: string;
      oldPriority: TRulePriority;
      newPriority: TRulePriority;
      reason: string;
    }>;
    sideEffects: string[];
    newConflicts: string[];
  };

  /** Resolution timing information */
  timing: {
    startedAt: Date;
    completedAt: Date;
    durationMs: number;
  };

  /** Resolution metadata */
  metadata: Record<string, unknown>;
}

/**
 * Priority Validation Result Interface
 * Result of priority hierarchy validation
 */
export interface IPriorityValidationResult {
  /** Validation success status */
  valid: boolean;

  /** Validation errors found */
  errors: Array<{
    errorType: 'circular-dependency' | 'invalid-hierarchy' | 'missing-priority' | 'duplicate-priority';
    ruleId: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;

  /** Validation warnings */
  warnings: Array<{
    warningType: 'suboptimal-priority' | 'potential-conflict' | 'performance-impact';
    ruleId: string;
    description: string;
    recommendation: string;
  }>;

  /** Priority hierarchy analysis */
  hierarchyAnalysis: {
    totalLevels: number;
    rulesPerLevel: Map<number, string[]>;
    orphanedRules: string[];
    circularDependencies: string[][];
  };

  /** Validation timing information */
  timing: {
    startedAt: Date;
    completedAt: Date;
    durationMs: number;
  };
}

/**
 * Priority Escalation Result Interface
 * Result of rule priority escalation operations
 */
export interface IPriorityEscalationResult {
  /** Escalation success status */
  success: boolean;

  /** Rule identifier that was escalated */
  ruleId: string;

  /** Previous priority level */
  previousPriority: TRulePriority;

  /** New priority level after escalation */
  newPriority: TRulePriority;

  /** Escalation reason provided */
  escalationReason: string;

  /** Escalation approval details */
  approval: {
    approved: boolean;
    approvedBy: string;
    approvalTimestamp: Date;
    approvalComments: string;
  };

  /** Impact of escalation */
  impact: {
    affectedRules: string[];
    priorityShifts: Array<{
      ruleId: string;
      oldPriority: TRulePriority;
      newPriority: TRulePriority;
    }>;
    performanceImpact: string;
  };

  /** Escalation timing information */
  timing: {
    requestedAt: Date;
    processedAt: Date;
    durationMs: number;
  };
}

// ============================================================================
// CONFLICT DETECTION AND ANALYSIS INTERFACES
// ============================================================================

/**
 * Rule Conflict Interface
 * Represents conflicts between governance rules
 */
export interface IRuleConflict {
  /** Unique conflict identifier */
  conflictId: string;

  /** Type of rule conflict */
  conflictType: TConflictType;

  /** Conflict severity level */
  severity: TConflictSeverity;

  /** Rules involved in the conflict */
  conflictingRules: TGovernanceRule[];

  /** Human-readable conflict description */
  description: string;

  /** When conflict was detected */
  detectedAt: Date;

  /** Conflict context information */
  context: {
    ruleSetId?: string;
    executionContextId?: string;
    triggeredBy?: string;
    environment?: string;
  };

  /** Impact analysis of the conflict */
  impactAnalysis: {
    affectedSystems: string[];
    potentialDamage: string;
    businessImpact: string;
    technicalImpact: string;
  };

  /** Additional conflict metadata */
  metadata: Record<string, unknown>;
}

/**
 * Conflict Analysis Result Interface
 * Result of comprehensive conflict analysis
 */
export interface IConflictAnalysisResult {
  /** Conflict identifier being analyzed */
  conflictId: string;

  /** Analysis success status */
  success: boolean;

  /** Detailed analysis findings */
  analysis: {
    rootCause: string;
    contributingFactors: string[];
    affectedComponents: string[];
    riskAssessment: {
      probability: 'low' | 'medium' | 'high';
      impact: 'low' | 'medium' | 'high' | 'critical';
      riskScore: number;
    };
  };

  /** Recommended resolution strategies */
  recommendedStrategies: Array<{
    strategy: TResolutionStrategy;
    confidence: number;
    estimatedEffort: 'low' | 'medium' | 'high';
    expectedOutcome: string;
  }>;

  /** Analysis timing information */
  timing: {
    startedAt: Date;
    completedAt: Date;
    durationMs: number;
  };

  /** Analysis metadata */
  metadata: Record<string, unknown>;
}

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

/**
 * Rule Priority Type
 * Defines priority levels for governance rules
 */
export type TRulePriority = {
  /** Priority level (1 = highest, 100 = lowest) */
  level: number;

  /** Priority category */
  category: 'critical' | 'high' | 'medium' | 'low' | 'background';

  /** Priority weight for calculations */
  weight: number;

  /** Priority inheritance settings */
  inheritance: {
    inheritable: boolean;
    inheritanceLevel: number;
  };
};

/**
 * Rule Priority Configuration Type
 * Configuration for rule priority management
 */
export type TRulePriorityConfig = {
  /** Default priority settings */
  defaultPriority: TRulePriority;

  /** Priority calculation method */
  calculationMethod: 'weighted' | 'hierarchical' | 'contextual' | 'hybrid';

  /** Priority conflict resolution strategy */
  conflictResolution: TPriorityResolutionStrategy;

  /** Maximum priority levels allowed */
  maxPriorityLevels: number;

  /** Priority validation rules */
  validationRules: Array<{
    rule: string;
    enforcement: 'strict' | 'warning' | 'advisory';
  }>;
};

/**
 * Priority Resolution Strategy Type
 * Available strategies for resolving priority conflicts
 */
export type TPriorityResolutionStrategy =
  | 'highest-wins'
  | 'lowest-wins'
  | 'average-priority'
  | 'context-based'
  | 'authority-hierarchy'
  | 'timestamp-based'
  | 'manual-resolution'
  | 'escalate';

/**
 * Priority Context Type
 * Context information for priority operations
 */
export type TPriorityContext = {
  /** Execution context identifier */
  contextId: string;

  /** Context type */
  contextType: 'global' | 'domain' | 'application' | 'user' | 'session';

  /** Context-specific priority modifiers */
  modifiers: Array<{
    type: 'boost' | 'reduce' | 'override';
    value: number;
    reason: string;
  }>;

  /** Context metadata */
  metadata: Record<string, unknown>;
};

/**
 * Rule Execution Order Type
 * Defines execution order for rules based on priority
 */
export type TRuleExecutionOrder = {
  /** Ordered list of rule identifiers */
  executionSequence: string[];

  /** Parallel execution groups */
  parallelGroups: Array<{
    groupId: string;
    rules: string[];
    priority: TRulePriority;
  }>;

  /** Execution dependencies */
  dependencies: Map<string, string[]>;

  /** Execution timing constraints */
  timingConstraints: Array<{
    ruleId: string;
    constraint: 'before' | 'after' | 'parallel' | 'exclusive';
    relatedRules: string[];
  }>;
};

/**
 * Conflict Type Enumeration
 * Types of conflicts that can occur between rules
 */
export type TConflictType =
  | 'direct-contradiction'
  | 'logical-inconsistency'
  | 'priority-conflict'
  | 'scope-overlap'
  | 'dependency-cycle'
  | 'resource-contention'
  | 'temporal-conflict'
  | 'authority-conflict'
  | 'version-conflict'
  | 'context-conflict';

/**
 * Conflict Severity Type
 * Severity levels for rule conflicts
 */
export type TConflictSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Resolution Strategy Type
 * Available strategies for resolving rule conflicts
 */
export type TResolutionStrategy =
  | 'priority-based'
  | 'context-aware'
  | 'temporal-precedence'
  | 'authority-hierarchy'
  | 'merge-compatible'
  | 'disable-conflicting'
  | 'escalate-human'
  | 'learned-pattern'
  | 'auto-resolve'
  | 'manual-intervention';

/**
 * Conflict Detection Context Type
 * Context for conflict detection operations
 */
export type TConflictDetectionContext = {
  /** Detection scope */
  scope: 'global' | 'domain' | 'ruleset' | 'targeted';

  /** Detection sensitivity */
  sensitivity: 'low' | 'medium' | 'high' | 'maximum';

  /** Detection filters */
  filters: Array<{
    type: 'rule-type' | 'priority' | 'context' | 'author';
    value: string;
    operator: 'equals' | 'contains' | 'matches' | 'greater' | 'less';
  }>;

  /** Detection options */
  options: {
    includeWarnings: boolean;
    deepAnalysis: boolean;
    predictiveDetection: boolean;
    realTimeMonitoring: boolean;
  };
};

/**
 * Time Range Type
 * Defines time ranges for statistics and reporting
 */
export type TTimeRange = {
  /** Start timestamp */
  startTime: Date;

  /** End timestamp */
  endTime: Date;

  /** Time zone information */
  timezone: string;

  /** Granularity for time-based aggregation */
  granularity: 'minute' | 'hour' | 'day' | 'week' | 'month' | 'year';
};

// ============================================================================
// FORWARD DECLARATIONS FOR COMPLEX INTERFACES
// ============================================================================

// These interfaces will be fully defined in other files to maintain modularity
export interface IStrategyValidationResult {}
export interface IConflictResolution {}
export interface IBatchResolutionResult {}
export interface IAutoResolutionResult {}
export interface IConflictHistory {}
export interface IPreventionRule {}
export interface ILearningResult {}
export interface IConflictPrediction {}
export interface IResolutionPerformanceMetrics {}
export interface IConflictReport {}
export interface IConflictStatistics {}

// Configuration types (forward declarations)
export interface TConflictAnalysisConfig {}
export interface TConflictResolutionOptions {}
export interface TBatchResolutionConfig {}
export interface TAutoResolutionConfig {}
export interface TPreventionConfig {}
export interface TLearningConfig {}
export interface TPredictionConfig {}
export interface TMonitoringConfig {}
export interface TConflictReportConfig {}
