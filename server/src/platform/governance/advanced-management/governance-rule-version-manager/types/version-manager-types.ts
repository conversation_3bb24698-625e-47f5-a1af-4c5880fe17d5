/**
 * ============================================================================
 * OA FRAMEWORK - GOVERNANCE RULE VERSION MANAGER TYPES
 * ============================================================================
 * 
 * Version: 1.0.0
 * Created: 2025-01-01
 * Author: OA Framework Development Team
 * 
 * Purpose: Type definitions for governance rule version management system
 * providing comprehensive data structures for version control, backward
 * compatibility validation, and version conflict resolution.
 * 
 * Compliance:
 * - Anti-Simplification Policy: Complete type definitions without shortcuts
 * - MEM-SAFE-002: Memory safety integration requirements
 * - OA Framework Standards: Type naming conventions (T prefix)
 * - Enterprise Grade: Production-ready type specifications
 * 
 * Authority: docs/core/development-standards.md (Version Manager v2.0)
 * ============================================================================
 */

// ============================================================================
// CORE IMPORTS
// ============================================================================

import {
  TGovernanceService
} from '../../../../../../../shared/src/types/platform/governance/rule-management-types';

import {
  TTrackingConfig
} from '../../../../../../../shared/src/types/platform/tracking/tracking-types';

// ============================================================================
// VERSION MANAGER DATA TYPES
// ============================================================================

/**
 * Version Manager Data Type
 * Core data structure for version management operations
 */
export type TVersionManagerData = TGovernanceService & {
  /** Version registry */
  versions: Map<string, TVersionRecord>;
  
  /** Rule version mappings */
  ruleVersions: Map<string, string[]>;
  
  /** Branch registry */
  branches: Map<string, TBranchRecord>;
  
  /** Tag registry */
  tags: Map<string, TTagRecord>;
  
  /** Conflict registry */
  conflicts: Map<string, TVersionConflictRecord>;
  
  /** Migration paths cache */
  migrationPaths: Map<string, TMigrationPathRecord>;
  
  /** Compatibility matrix */
  compatibilityMatrix: Map<string, TCompatibilityMatrix>;
  
  /** Performance metrics */
  performanceMetrics: TVersionManagerPerformanceMetrics;
  
  /** Configuration settings */
  configuration: TVersionManagerConfiguration;
  
  /** Authority data */
  authorityData: TAuthorityData;
};

/**
 * Version Manager Configuration Type
 * Configuration settings for version management operations
 */
export type TVersionManagerConfiguration = TTrackingConfig & {
  /** Maximum versions per rule */
  maxVersionsPerRule: number;
  
  /** Version retention policy */
  retentionPolicy: {
    /** Days to retain deprecated versions */
    deprecatedRetentionDays: number;
    /** Days to retain archived versions */
    archivedRetentionDays: number;
    /** Auto-cleanup enabled */
    autoCleanupEnabled: boolean;
    /** Cleanup interval in hours */
    cleanupIntervalHours: number;
  };
  
  /** Compatibility validation settings */
  compatibilityValidation: {
    /** Enable strict compatibility checking */
    strictMode: boolean;
    /** Breaking change detection enabled */
    breakingChangeDetection: boolean;
    /** Auto-migration generation */
    autoMigrationGeneration: boolean;
    /** Compatibility cache TTL in minutes */
    cacheTtlMinutes: number;
  };
  
  /** Conflict resolution settings */
  conflictResolution: {
    /** Default resolution strategy */
    defaultStrategy: TConflictResolutionStrategy;
    /** Auto-resolution enabled */
    autoResolutionEnabled: boolean;
    /** Escalation threshold */
    escalationThreshold: number;
    /** Resolution timeout in minutes */
    resolutionTimeoutMinutes: number;
  };
  
  /** Performance optimization settings */
  performance: {
    /** Enable version caching */
    enableCaching: boolean;
    /** Cache size limit */
    cacheSizeLimit: number;
    /** Enable compression */
    enableCompression: boolean;
    /** Batch operation size */
    batchOperationSize: number;
  };
  
  /** Security settings */
  security: {
    /** Enable version signing */
    enableVersionSigning: boolean;
    /** Require author verification */
    requireAuthorVerification: boolean;
    /** Enable audit logging */
    enableAuditLogging: boolean;
    /** Encryption enabled */
    encryptionEnabled: boolean;
  };
};

/**
 * Version Record Type
 * Complete version information and metadata
 */
export type TVersionRecord = {
  /** Version identifier */
  versionId: string;
  
  /** Rule identifier */
  ruleId: string;
  
  /** Version number (semantic versioning) */
  version: string;
  
  /** Version description */
  description?: string;
  
  /** Rule content */
  ruleContent: any;
  
  /** Version status */
  status: 'draft' | 'active' | 'deprecated' | 'archived';
  
  /** Creation timestamp */
  createdAt: Date;
  
  /** Last update timestamp */
  updatedAt: Date;
  
  /** Author information */
  author: TAuthorInfo;
  
  /** Version metadata */
  metadata: TVersionMetadata;
  
  /** Change log */
  changeLog: TChangeLogEntry[];
  
  /** Parent version ID */
  parentVersionId?: string;
  
  /** Child version IDs */
  childVersionIds: string[];
  
  /** Branch information */
  branchInfo?: TBranchRecord;

  /** Tag information */
  tags: TTagRecord[];
  
  /** Compatibility information */
  compatibilityInfo: TCompatibilityInfo;
  
  /** Performance metrics */
  performanceMetrics: TVersionPerformanceMetrics;
  
  /** Security information */
  securityInfo: TVersionSecurityInfo;
};

/**
 * Branch Record Type
 * Branch information and metadata
 */
export type TBranchRecord = {
  /** Branch identifier */
  branchId: string;
  
  /** Branch name */
  branchName: string;
  
  /** Source version ID */
  sourceVersionId: string;
  
  /** Current version ID */
  currentVersionId: string;
  
  /** Branch status */
  status: 'active' | 'merged' | 'abandoned';
  
  /** Creation timestamp */
  createdAt: Date;
  
  /** Branch author */
  author: TAuthorInfo;
  
  /** Branch description */
  description?: string;
  
  /** Branch metadata */
  metadata: TBranchMetadata;
  
  /** Merge information if merged */
  mergeInfo?: TMergeInfo;
  
  /** Branch history */
  history: TBranchHistoryEntry[];
};

/**
 * Tag Record Type
 * Version tag information and metadata
 */
export type TTagRecord = {
  /** Tag identifier */
  tagId: string;
  
  /** Tag name */
  tagName: string;
  
  /** Version identifier */
  versionId: string;
  
  /** Tag description */
  description?: string;
  
  /** Creation timestamp */
  createdAt: Date;
  
  /** Tag author */
  author: TAuthorInfo;
  
  /** Tag type */
  tagType: 'release' | 'milestone' | 'hotfix' | 'feature' | 'custom';
  
  /** Tag metadata */
  metadata: TTagMetadata;
  
  /** Tag status */
  status: 'active' | 'deprecated' | 'archived';
};

// ============================================================================
// CONFLICT AND RESOLUTION TYPES
// ============================================================================

/**
 * Version Conflict Record Type
 * Information about version conflicts
 */
export type TVersionConflictRecord = {
  /** Conflict identifier */
  conflictId: string;

  /** Conflict type */
  conflictType: 'version-mismatch' | 'rule-contradiction' | 'dependency-conflict' | 'merge-conflict';

  /** Involved version IDs */
  involvedVersionIds: string[];

  /** Conflict description */
  description: string;

  /** Conflict severity */
  severity: 'low' | 'medium' | 'high' | 'critical';

  /** Detection timestamp */
  detectedAt: Date;

  /** Conflict status */
  status: 'detected' | 'analyzing' | 'resolving' | 'resolved' | 'escalated';

  /** Resolution attempts */
  resolutionAttempts: TResolutionAttempt[];

  /** Suggested resolutions */
  suggestedResolutions: TSuggestedResolution[];

  /** Conflict metadata */
  metadata: TConflictMetadata;
};

/**
 * Migration Path Record Type
 * Information about version migration paths
 */
export type TMigrationPathRecord = {
  /** Migration path identifier */
  pathId: string;

  /** Source version ID */
  sourceVersionId: string;

  /** Target version ID */
  targetVersionId: string;

  /** Migration steps */
  steps: TMigrationStep[];

  /** Estimated duration in minutes */
  estimatedDuration: number;

  /** Migration complexity */
  complexity: 'simple' | 'moderate' | 'complex' | 'critical';

  /** Migration status */
  status: 'planned' | 'executing' | 'completed' | 'failed' | 'cancelled';

  /** Creation timestamp */
  createdAt: Date;

  /** Execution history */
  executionHistory: TMigrationExecution[];

  /** Migration metadata */
  metadata: TMigrationMetadata;
};

/**
 * Compatibility Matrix Type
 * Compatibility information between versions
 */
export type TCompatibilityMatrix = {
  /** Source version ID */
  sourceVersionId: string;

  /** Target version ID */
  targetVersionId: string;

  /** Compatibility status */
  compatible: boolean;

  /** Compatibility score (0-100) */
  compatibilityScore: number;

  /** Breaking changes */
  breakingChanges: TBreakingChange[];

  /** Compatibility issues */
  issues: TCompatibilityIssue[];

  /** Migration required */
  migrationRequired: boolean;

  /** Recommended migration path */
  recommendedMigrationPath?: string;

  /** Last validation timestamp */
  lastValidated: Date;

  /** Validation metadata */
  validationMetadata: TValidationMetadata;
};

// ============================================================================
// SUPPORTING DATA TYPES
// ============================================================================

/**
 * Author Information Type
 * Author details and verification
 */
export type TAuthorInfo = {
  /** Author identifier */
  authorId: string;

  /** Author name */
  name: string;

  /** Author email */
  email: string;

  /** Author role */
  role: string;

  /** Verification status */
  verified: boolean;

  /** Verification timestamp */
  verifiedAt?: Date;
};

/**
 * Version Metadata Type
 * Extended version metadata
 */
export type TVersionMetadata = {
  /** Custom properties */
  properties: Record<string, unknown>;

  /** Labels and tags */
  labels: string[];

  /** Categories */
  categories: string[];

  /** Priority level */
  priority: 'low' | 'medium' | 'high' | 'critical';

  /** Environment compatibility */
  environments: string[];

  /** Dependencies */
  dependencies: TVersionDependency[];

  /** Checksum for integrity */
  checksum: string;

  /** Size in bytes */
  size: number;
};

/**
 * Change Log Entry Type
 * Individual change log entry
 */
export type TChangeLogEntry = {
  /** Entry identifier */
  entryId: string;

  /** Change timestamp */
  timestamp: Date;

  /** Change author */
  author: TAuthorInfo;

  /** Change type */
  changeType: 'create' | 'update' | 'delete' | 'merge' | 'branch' | 'tag';

  /** Change description */
  description: string;

  /** Affected components */
  affectedComponents: string[];

  /** Change impact level */
  impactLevel: 'low' | 'medium' | 'high' | 'critical';

  /** Before state */
  beforeState?: any;

  /** After state */
  afterState?: any;

  /** Additional metadata */
  metadata: Record<string, unknown>;
};

// ============================================================================
// PERFORMANCE AND METRICS TYPES
// ============================================================================

/**
 * Version Manager Performance Metrics Type
 * Performance tracking for version management operations
 */
export type TVersionManagerPerformanceMetrics = {
  /** Total operations performed */
  totalOperations: number;

  /** Average operation duration in milliseconds */
  averageOperationDuration: number;

  /** Operations per second */
  operationsPerSecond: number;

  /** Memory usage in bytes */
  memoryUsage: number;

  /** Cache hit rate percentage */
  cacheHitRate: number;

  /** Error rate percentage */
  errorRate: number;

  /** Last metrics update */
  lastUpdated: Date;

  /** Operation breakdown */
  operationBreakdown: {
    create: TOperationMetrics;
    update: TOperationMetrics;
    delete: TOperationMetrics;
    query: TOperationMetrics;
    merge: TOperationMetrics;
    migrate: TOperationMetrics;
  };
};

/**
 * Version Performance Metrics Type
 * Performance metrics for individual versions
 */
export type TVersionPerformanceMetrics = {
  /** Access count */
  accessCount: number;

  /** Last accessed timestamp */
  lastAccessed: Date;

  /** Average access duration */
  averageAccessDuration: number;

  /** Validation duration */
  validationDuration: number;

  /** Storage efficiency */
  storageEfficiency: number;

  /** Compression ratio */
  compressionRatio: number;
};

/**
 * Operation Metrics Type
 * Metrics for specific operation types
 */
export type TOperationMetrics = {
  /** Total count */
  count: number;

  /** Average duration in milliseconds */
  averageDuration: number;

  /** Success rate percentage */
  successRate: number;

  /** Error count */
  errorCount: number;

  /** Last operation timestamp */
  lastOperation: Date;
};

// ============================================================================
// CONFLICT RESOLUTION TYPES
// ============================================================================

/**
 * Conflict Resolution Strategy Type
 * Available conflict resolution strategies
 */
export type TConflictResolutionStrategy =
  | 'priority-based'
  | 'timestamp-based'
  | 'author-hierarchy'
  | 'merge-compatible'
  | 'manual-resolution'
  | 'escalate';

/**
 * Resolution Attempt Type
 * Information about conflict resolution attempts
 */
export type TResolutionAttempt = {
  /** Attempt identifier */
  attemptId: string;

  /** Attempt timestamp */
  timestamp: Date;

  /** Resolution strategy used */
  strategy: TConflictResolutionStrategy;

  /** Attempt result */
  result: 'success' | 'failed' | 'partial';

  /** Error messages if failed */
  errors: string[];

  /** Resolution author */
  resolvedBy: string;

  /** Duration in milliseconds */
  duration: number;

  /** Attempt metadata */
  metadata: Record<string, unknown>;
};

/**
 * Suggested Resolution Type
 * AI-generated resolution suggestions
 */
export type TSuggestedResolution = {
  /** Suggestion identifier */
  suggestionId: string;

  /** Suggested strategy */
  strategy: TConflictResolutionStrategy;

  /** Confidence score (0-100) */
  confidence: number;

  /** Resolution description */
  description: string;

  /** Expected outcome */
  expectedOutcome: string;

  /** Risk assessment */
  riskLevel: 'low' | 'medium' | 'high' | 'critical';

  /** Implementation steps */
  steps: string[];

  /** Estimated duration */
  estimatedDuration: number;
};

// ============================================================================
// MIGRATION TYPES
// ============================================================================

/**
 * Migration Step Type
 * Individual migration step information
 */
export type TMigrationStep = {
  /** Step identifier */
  stepId: string;

  /** Step order */
  order: number;

  /** Step type */
  stepType: 'validation' | 'transformation' | 'verification' | 'rollback';

  /** Step description */
  description: string;

  /** Step implementation */
  implementation: string;

  /** Dependencies */
  dependencies: string[];

  /** Estimated duration */
  estimatedDuration: number;

  /** Rollback implementation */
  rollbackImplementation?: string;

  /** Step metadata */
  metadata: Record<string, unknown>;
};

/**
 * Migration Execution Type
 * Migration execution history and results
 */
export type TMigrationExecution = {
  /** Execution identifier */
  executionId: string;

  /** Execution timestamp */
  executedAt: Date;

  /** Execution status */
  status: 'running' | 'completed' | 'failed' | 'cancelled';

  /** Executed steps */
  executedSteps: number;

  /** Total steps */
  totalSteps: number;

  /** Execution duration */
  duration: number;

  /** Execution errors */
  errors: string[];

  /** Execution results */
  results: Record<string, unknown>;

  /** Executed by */
  executedBy: string;
};

// ============================================================================
// COMPATIBILITY AND VALIDATION TYPES
// ============================================================================

/**
 * Breaking Change Type
 * Information about breaking changes between versions
 */
export type TBreakingChange = {
  /** Change identifier */
  changeId: string;

  /** Change type */
  changeType: 'api-change' | 'schema-change' | 'behavior-change' | 'dependency-change';

  /** Change description */
  description: string;

  /** Impact level */
  impactLevel: 'low' | 'medium' | 'high' | 'critical';

  /** Affected components */
  affectedComponents: string[];

  /** Migration required */
  migrationRequired: boolean;

  /** Migration complexity */
  migrationComplexity: 'simple' | 'moderate' | 'complex';

  /** Workaround available */
  workaroundAvailable: boolean;

  /** Workaround description */
  workaroundDescription?: string;
};

/**
 * Compatibility Issue Type
 * Specific compatibility issues between versions
 */
export type TCompatibilityIssue = {
  /** Issue identifier */
  issueId: string;

  /** Issue type */
  issueType: 'warning' | 'error' | 'deprecation' | 'incompatibility';

  /** Issue description */
  description: string;

  /** Severity level */
  severity: 'low' | 'medium' | 'high' | 'critical';

  /** Component affected */
  component: string;

  /** Resolution suggestion */
  resolutionSuggestion?: string;

  /** Auto-fixable */
  autoFixable: boolean;

  /** Issue metadata */
  metadata: Record<string, unknown>;
};

// ============================================================================
// METADATA AND CONFIGURATION TYPES
// ============================================================================

/**
 * Branch Metadata Type
 * Extended branch metadata
 */
export type TBranchMetadata = {
  /** Branch purpose */
  purpose: string;

  /** Target release */
  targetRelease?: string;

  /** Feature flags */
  featureFlags: string[];

  /** Branch labels */
  labels: string[];

  /** Custom properties */
  properties: Record<string, unknown>;
};

/**
 * Tag Metadata Type
 * Extended tag metadata
 */
export type TTagMetadata = {
  /** Release notes */
  releaseNotes?: string;

  /** Associated milestone */
  milestone?: string;

  /** Tag labels */
  labels: string[];

  /** Custom properties */
  properties: Record<string, unknown>;
};

/**
 * Conflict Metadata Type
 * Extended conflict metadata
 */
export type TConflictMetadata = {
  /** Conflict source */
  source: string;

  /** Detection method */
  detectionMethod: 'automatic' | 'manual' | 'scheduled';

  /** Related conflicts */
  relatedConflicts: string[];

  /** Custom properties */
  properties: Record<string, unknown>;
};

/**
 * Migration Metadata Type
 * Extended migration metadata
 */
export type TMigrationMetadata = {
  /** Migration category */
  category: string;

  /** Risk assessment */
  riskAssessment: 'low' | 'medium' | 'high' | 'critical';

  /** Rollback strategy */
  rollbackStrategy: string;

  /** Testing requirements */
  testingRequirements: string[];

  /** Custom properties */
  properties: Record<string, unknown>;
};

/**
 * Validation Metadata Type
 * Extended validation metadata
 */
export type TValidationMetadata = {
  /** Validation method */
  validationMethod: string;

  /** Validation rules applied */
  rulesApplied: string[];

  /** Validation duration */
  duration: number;

  /** Custom properties */
  properties: Record<string, unknown>;
};

// ============================================================================
// UTILITY AND HELPER TYPES
// ============================================================================

/**
 * Version Dependency Type
 * Version dependency information
 */
export type TVersionDependency = {
  /** Dependency identifier */
  dependencyId: string;

  /** Dependency type */
  dependencyType: 'required' | 'optional' | 'development';

  /** Version constraint */
  versionConstraint: string;

  /** Dependency description */
  description?: string;
};

/**
 * Branch History Entry Type
 * Branch history tracking
 */
export type TBranchHistoryEntry = {
  /** Entry identifier */
  entryId: string;

  /** Entry timestamp */
  timestamp: Date;

  /** Action performed */
  action: 'create' | 'update' | 'merge' | 'abandon';

  /** Action description */
  description: string;

  /** Performed by */
  performedBy: string;

  /** Entry metadata */
  metadata: Record<string, unknown>;
};

/**
 * Merge Information Type
 * Detailed merge operation information
 */
export type TMergeInfo = {
  /** Merge identifier */
  mergeId: string;

  /** Target version ID */
  targetVersionId: string;

  /** Source version ID */
  sourceVersionId: string;

  /** Merge timestamp */
  mergedAt: Date;

  /** Merge author */
  mergedBy: string;

  /** Merge strategy used */
  strategy: 'fast-forward' | 'three-way' | 'squash' | 'rebase';

  /** Conflicts encountered */
  conflicts: TConflictInfo[];

  /** Merge result status */
  status: 'success' | 'failed' | 'partial';

  /** Merge duration */
  duration: number;

  /** Merge metadata */
  metadata: Record<string, unknown>;
};

/**
 * Conflict Information Type
 * Basic conflict information
 */
export type TConflictInfo = {
  /** Conflict identifier */
  conflictId: string;

  /** Conflict type */
  type: string;

  /** Conflict description */
  description: string;

  /** Severity level */
  severity: 'low' | 'medium' | 'high' | 'critical';

  /** Resolution status */
  resolved: boolean;

  /** Resolution method */
  resolutionMethod?: string;
};

/**
 * Version Security Information Type
 * Security-related version information
 */
export type TVersionSecurityInfo = {
  /** Digital signature */
  signature?: string;

  /** Signature verification status */
  signatureVerified: boolean;

  /** Encryption status */
  encrypted: boolean;

  /** Security hash */
  securityHash: string;

  /** Security metadata */
  securityMetadata: Record<string, unknown>;
};

/**
 * Authority Data Type
 * Authority and governance information
 */
export type TAuthorityData = {
  /** Authority identifier */
  authorityId: string;

  /** Authority name */
  authorityName: string;

  /** Authority version */
  authorityVersion: string;

  /** Governance policies */
  governancePolicies: string[];

  /** Compliance requirements */
  complianceRequirements: string[];

  /** Authority metadata */
  metadata: Record<string, unknown>;
};

/**
 * Compatibility Information Type
 * Version compatibility details
 */
export type TCompatibilityInfo = {
  /** Compatible versions */
  compatibleVersions: string[];

  /** Incompatible versions */
  incompatibleVersions: string[];

  /** Compatibility score */
  compatibilityScore: number;

  /** Last compatibility check */
  lastCompatibilityCheck: Date;

  /** Compatibility metadata */
  metadata: Record<string, unknown>;
};
