/**
 * @file Governance Rule Conflict Resolver Tests
 * @filepath server/src/platform/governance/advanced-management/__tests__/GovernanceRuleConflictResolver.test.ts
 * @task-id G-TSK-CONFLICT-RESOLVER-TESTS
 * @component governance-rule-conflict-resolver-tests
 * @reference governance-context.CONFLICT.TEST.001
 * @template enterprise-governance-tests
 * @tier T1
 * @context governance-context
 * @category Advanced Management - Conflict Resolution Tests
 * @created 2025-09-02
 * @modified 2025-09-02
 * 
 * @description
 * Comprehensive test suite for GovernanceRuleConflictResolver providing:
 * - Complete priority management and conflict resolution testing
 * - Advanced conflict detection and analysis validation
 * - Performance and memory safety compliance testing
 * - Resilient timing integration verification
 * - BaseTrackingService inheritance validation
 * - Enterprise-grade error handling and edge case testing
 * 
 * @compliance
 * - Testing Phase Governance: Production value focus, comprehensive coverage
 * - Anti-Simplification Policy: Complete test functionality, no shortcuts
 * - Memory Safety: Memory leak prevention testing (MEM-SAFE-002)
 * - Performance: <2000ms operation testing for enterprise-scale systems
 * 
 * @coverage-target
 * - Statements: 100%
 * - Branches: 100% 
 * - Functions: 100%
 * - Lines: 100%
 * - Priority Management: 100% (MANDATORY FOCUS)
 * 
 * <AUTHOR> Consultancy - Advanced Governance Team
 * @version 1.0.0
 * @since 2025-09-02
 */

// ============================================================================
// IMPORTS AND DEPENDENCIES
// ============================================================================

// Testing Framework
import { describe, test, expect, beforeEach, afterEach, jest } from '@jest/globals';

// Component Under Test
import { GovernanceRuleConflictResolver } from '../GovernanceRuleConflictResolver';

// Test Utilities and Mocks
import { createMockGovernanceRule, createMockRuleSet } from '../../__mocks__/governance-test-utils';

// Types and Interfaces
import {
  TGovernanceRule,
  TGovernanceRuleSet
} from '../../../../../shared/src/types/platform/governance/governance-types';

import {
  TRulePriorityConfig,
  TPriorityResolutionStrategy,
  TConflictDetectionContext,
  IPriorityConflict,
  IRuleConflict
} from '../governance-rule-conflict-resolver/conflict-resolver-interfaces';

// ============================================================================
// TEST CONFIGURATION AND CONSTANTS
// ============================================================================

/** Test timeout for individual tests */
const TEST_TIMEOUT = 30000; // 30 seconds

/** Test configuration for conflict resolver */
const TEST_CONFIG = {
  maxConcurrentResolutions: 5,
  conflictDetectionIntervalMs: 10000,
  resolutionTimeoutMs: 5000,
  maxResolutionAttempts: 2,
  conflictHistoryRetentionDays: 30,
  learningAlgorithmEnabled: true,
  proactiveDetectionEnabled: false, // Disabled for testing
  realTimeMonitoringEnabled: false, // Disabled for testing
  conflictCacheTtlMs: 60000,
  resolutionStrategyCacheSize: 100
};

/** Test priority configuration */
const TEST_PRIORITY_CONFIG: TRulePriorityConfig = {
  defaultPriority: {
    level: 50,
    category: 'medium',
    weight: 1.0,
    inheritance: {
      inheritable: true,
      inheritanceLevel: 1
    }
  },
  calculationMethod: 'weighted',
  conflictResolution: 'priority-based',
  maxPriorityLevels: 100,
  validationRules: [
    { rule: 'no-duplicate-priorities', enforcement: 'warning' },
    { rule: 'valid-priority-range', enforcement: 'strict' }
  ]
};

// ============================================================================
// TEST SUITE SETUP AND TEARDOWN
// ============================================================================

describe('GovernanceRuleConflictResolver', () => {
  let conflictResolver: GovernanceRuleConflictResolver;
  let mockRules: TGovernanceRule[];
  let mockRuleSet: TGovernanceRuleSet;

  beforeEach(async () => {
    // Create fresh instance for each test
    conflictResolver = new GovernanceRuleConflictResolver(TEST_CONFIG, TEST_PRIORITY_CONFIG);
    
    // Initialize the service
    await conflictResolver.initialize();
    
    // Create mock test data
    mockRules = [
      createMockGovernanceRule('rule-1', { priority: { level: 10, category: 'high' } }),
      createMockGovernanceRule('rule-2', { priority: { level: 20, category: 'medium' } }),
      createMockGovernanceRule('rule-3', { priority: { level: 10, category: 'high' } }), // Duplicate priority
      createMockGovernanceRule('rule-4', { priority: { level: 30, category: 'low' } })
    ];
    
    mockRuleSet = createMockRuleSet('test-ruleset', mockRules);
  });

  afterEach(async () => {
    // Ensure proper cleanup
    if (conflictResolver) {
      await conflictResolver.shutdown();
    }
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  // ============================================================================
  // INITIALIZATION AND LIFECYCLE TESTS
  // ============================================================================

  describe('Initialization and Lifecycle', () => {
    test('should initialize successfully with default configuration', async () => {
      const resolver = new GovernanceRuleConflictResolver();
      
      await resolver.initialize();
      
      expect(resolver.isReady()).toBe(true);
      expect(resolver.isHealthy()).toBe(true);
      
      await resolver.shutdown();
    }, TEST_TIMEOUT);

    test('should initialize with custom configuration', async () => {
      const customConfig = { ...TEST_CONFIG, maxConcurrentResolutions: 10 };
      const resolver = new GovernanceRuleConflictResolver(customConfig);
      
      await resolver.initialize();
      
      expect(resolver.isReady()).toBe(true);
      
      const metrics = await resolver.getMetrics();
      expect(metrics).toBeDefined();
      
      await resolver.shutdown();
    }, TEST_TIMEOUT);

    test('should handle initialization errors gracefully', async () => {
      // Mock initialization failure
      const resolver = new GovernanceRuleConflictResolver();
      
      // Mock the doInitialize method to throw an error
      const originalDoInitialize = (resolver as any).doInitialize;
      (resolver as any).doInitialize = jest.fn().mockRejectedValue(new Error('Initialization failed'));
      
      await expect(resolver.initialize()).rejects.toThrow('Initialization failed');
      
      // Restore original method
      (resolver as any).doInitialize = originalDoInitialize;
    }, TEST_TIMEOUT);

    test('should shutdown gracefully and clean up resources', async () => {
      await conflictResolver.shutdown();
      
      expect(conflictResolver.isReady()).toBe(false);
      
      // Verify collections are cleared
      const metrics = await conflictResolver.getMetrics();
      expect(metrics.activeResolutions).toBe(0);
    }, TEST_TIMEOUT);
  });

  // ============================================================================
  // PRIORITY MANAGEMENT TESTS (MANDATORY FOCUS)
  // ============================================================================

  describe('Priority Management', () => {
    test('should manage rule priorities successfully', async () => {
      const result = await conflictResolver.manageRulePriorities(mockRules, TEST_PRIORITY_CONFIG);
      
      expect(result.success).toBe(false); // Should detect conflicts
      expect(result.rulesProcessed).toBe(mockRules.length);
      expect(result.priorityAssignments.size).toBe(mockRules.length);
      expect(result.conflictsDetected.length).toBeGreaterThan(0); // Should detect duplicate priorities
      expect(result.timing.durationMs).toBeGreaterThan(0);
    }, TEST_TIMEOUT);

    test('should detect priority conflicts correctly', async () => {
      const conflicts = await conflictResolver.detectPriorityConflicts(mockRuleSet);
      
      expect(conflicts.length).toBeGreaterThan(0);
      
      // Should detect duplicate priority conflict between rule-1 and rule-3
      const duplicateConflict = conflicts.find(c => 
        c.conflictType === 'duplicate-priority' &&
        c.conflictingRules.includes('rule-1') &&
        c.conflictingRules.includes('rule-3')
      );
      
      expect(duplicateConflict).toBeDefined();
      expect(duplicateConflict!.severity).toBe('medium');
    }, TEST_TIMEOUT);

    test('should resolve priority conflicts using different strategies', async () => {
      // First detect conflicts
      const conflicts = await conflictResolver.detectPriorityConflicts(mockRuleSet);
      expect(conflicts.length).toBeGreaterThan(0);
      
      // Test highest-wins strategy
      const highestWinsResult = await conflictResolver.resolvePriorityConflicts(
        conflicts, 
        'highest-wins'
      );
      
      expect(highestWinsResult.success).toBe(true);
      expect(highestWinsResult.conflictsResolved).toBe(conflicts.length);
      expect(highestWinsResult.strategyUsed).toBe('highest-wins');
      expect(highestWinsResult.updatedPriorities.size).toBeGreaterThan(0);
      
      // Test lowest-wins strategy
      const lowestWinsResult = await conflictResolver.resolvePriorityConflicts(
        conflicts, 
        'lowest-wins'
      );
      
      expect(lowestWinsResult.success).toBe(true);
      expect(lowestWinsResult.strategyUsed).toBe('lowest-wins');
      
      // Test average-priority strategy
      const averageResult = await conflictResolver.resolvePriorityConflicts(
        conflicts, 
        'average-priority'
      );
      
      expect(averageResult.success).toBe(true);
      expect(averageResult.strategyUsed).toBe('average-priority');
    }, TEST_TIMEOUT);

    test('should validate priority hierarchy correctly', async () => {
      const validationResult = await conflictResolver.validatePriorityHierarchy(mockRuleSet);
      
      expect(validationResult).toBeDefined();
      expect(validationResult.hierarchyAnalysis).toBeDefined();
      expect(validationResult.hierarchyAnalysis.totalLevels).toBeGreaterThan(0);
      expect(validationResult.hierarchyAnalysis.rulesPerLevel.size).toBeGreaterThan(0);
      expect(validationResult.timing.durationMs).toBeGreaterThan(0);
      
      // Should have warnings or errors due to duplicate priorities
      expect(validationResult.errors.length + validationResult.warnings.length).toBeGreaterThan(0);
    }, TEST_TIMEOUT);

    test('should generate priority-based execution order', async () => {
      const executionOrder = await conflictResolver.getPriorityExecutionOrder(mockRuleSet);
      
      expect(executionOrder.executionSequence).toBeDefined();
      expect(executionOrder.executionSequence.length).toBe(mockRules.length);
      expect(executionOrder.parallelGroups).toBeDefined();
      expect(executionOrder.dependencies).toBeDefined();
      expect(executionOrder.timingConstraints).toBeDefined();
      
      // Verify rules are ordered by priority (lower level = higher priority)
      const firstRuleId = executionOrder.executionSequence[0];
      const lastRuleId = executionOrder.executionSequence[executionOrder.executionSequence.length - 1];
      
      expect(['rule-1', 'rule-3']).toContain(firstRuleId); // Highest priority rules
      expect(lastRuleId).toBe('rule-4'); // Lowest priority rule
    }, TEST_TIMEOUT);

    test('should escalate rule priority successfully', async () => {
      const ruleId = 'rule-2';
      const escalationReason = 'Critical business requirement';
      
      const escalationResult = await conflictResolver.escalateRulePriority(ruleId, escalationReason);
      
      expect(escalationResult.success).toBe(true);
      expect(escalationResult.ruleId).toBe(ruleId);
      expect(escalationResult.escalationReason).toBe(escalationReason);
      expect(escalationResult.newPriority.level).toBeLessThan(escalationResult.previousPriority.level);
      expect(escalationResult.approval.approved).toBe(true);
      expect(escalationResult.impact.affectedRules).toContain(ruleId);
      expect(escalationResult.timing.durationMs).toBeGreaterThan(0);
    }, TEST_TIMEOUT);

    test('should handle priority escalation for non-existent rule', async () => {
      const nonExistentRuleId = 'non-existent-rule';
      
      await expect(
        conflictResolver.escalateRulePriority(nonExistentRuleId, 'Test escalation')
      ).rejects.toThrow(`Rule ${nonExistentRuleId} not found in priority registry`);
    }, TEST_TIMEOUT);
  });

  // ============================================================================
  // CONFLICT DETECTION AND ANALYSIS TESTS
  // ============================================================================

  describe('Conflict Detection and Analysis', () => {
    test('should detect conflicts in rule set', async () => {
      const context: TConflictDetectionContext = {
        scope: 'ruleset',
        sensitivity: 'medium',
        filters: [],
        options: {
          includeWarnings: true,
          deepAnalysis: true,
          predictiveDetection: false,
          realTimeMonitoring: false
        }
      };
      
      const conflicts = await conflictResolver.detectConflicts(mockRuleSet, context);
      
      expect(conflicts).toBeDefined();
      expect(Array.isArray(conflicts)).toBe(true);
      
      // Should detect at least priority conflicts
      const priorityConflicts = conflicts.filter(c => c.conflictType === 'priority-conflict');
      expect(priorityConflicts.length).toBeGreaterThan(0);
    }, TEST_TIMEOUT);

    test('should analyze conflicts comprehensively', async () => {
      // First detect conflicts
      const context: TConflictDetectionContext = {
        scope: 'ruleset',
        sensitivity: 'high',
        filters: [],
        options: {
          includeWarnings: true,
          deepAnalysis: true,
          predictiveDetection: false,
          realTimeMonitoring: false
        }
      };
      
      const conflicts = await conflictResolver.detectConflicts(mockRuleSet, context);
      
      if (conflicts.length > 0) {
        const analysisConfig = {}; // Use default analysis config
        const analysisResults = await conflictResolver.analyzeConflicts(conflicts, analysisConfig);
        
        expect(analysisResults.length).toBe(conflicts.length);
        
        analysisResults.forEach(result => {
          expect(result.conflictId).toBeDefined();
          expect(result.success).toBe(true);
          expect(result.analysis).toBeDefined();
          expect(result.recommendedStrategies).toBeDefined();
          expect(result.timing.durationMs).toBeGreaterThan(0);
        });
      }
    }, TEST_TIMEOUT);

    test('should validate resolution strategies', async () => {
      // Create mock conflicts
      const mockConflicts: IRuleConflict[] = [
        {
          conflictId: 'test-conflict-1',
          conflictType: 'priority-conflict',
          severity: 'medium',
          conflictingRules: [mockRules[0], mockRules[2]],
          description: 'Test priority conflict',
          detectedAt: new Date(),
          context: {},
          impactAnalysis: {
            affectedSystems: ['test-system'],
            potentialDamage: 'Low',
            businessImpact: 'Minimal',
            technicalImpact: 'Low'
          },
          metadata: {}
        }
      ];
      
      const strategies = ['priority-based', 'context-aware'];
      
      const validationResults = await conflictResolver.validateResolutionStrategies(
        mockConflicts, 
        strategies
      );
      
      expect(validationResults.length).toBe(Math.min(mockConflicts.length, strategies.length));
    }, TEST_TIMEOUT);
  });

  // ============================================================================
  // CONFLICT RESOLUTION OPERATIONS TESTS
  // ============================================================================

  describe('Conflict Resolution Operations', () => {
    test('should resolve individual conflict successfully', async () => {
      // First detect conflicts to get a real conflict ID
      const context: TConflictDetectionContext = {
        scope: 'ruleset',
        sensitivity: 'medium',
        filters: [],
        options: {
          includeWarnings: true,
          deepAnalysis: true,
          predictiveDetection: false,
          realTimeMonitoring: false
        }
      };

      const conflicts = await conflictResolver.detectConflicts(mockRuleSet, context);

      if (conflicts.length > 0) {
        const conflictId = conflicts[0].conflictId;

        const resolution = await conflictResolver.resolveConflict(
          conflictId,
          'priority-based'
        );

        expect(resolution).toBeDefined();
        expect(resolution.conflictId).toBe(conflictId);
        expect(resolution.strategy).toBe('priority-based');
        expect(resolution.outcome).toBeDefined();
        expect(resolution.timing.durationMs).toBeGreaterThan(0);
      }
    }, TEST_TIMEOUT);

    test('should handle resolution of non-existent conflict', async () => {
      const nonExistentConflictId = 'non-existent-conflict';

      await expect(
        conflictResolver.resolveConflict(nonExistentConflictId, 'priority-based')
      ).rejects.toThrow(`Conflict ${nonExistentConflictId} not found`);
    }, TEST_TIMEOUT);

    test('should resolve conflicts in batch successfully', async () => {
      // First detect conflicts
      const context: TConflictDetectionContext = {
        scope: 'ruleset',
        sensitivity: 'medium',
        filters: [],
        options: {
          includeWarnings: true,
          deepAnalysis: true,
          predictiveDetection: false,
          realTimeMonitoring: false
        }
      };

      const conflicts = await conflictResolver.detectConflicts(mockRuleSet, context);

      if (conflicts.length > 0) {
        const batchConfig = {
          maxConcurrentResolutions: 3,
          continueOnFailure: true,
          timeoutMs: 10000
        };

        const batchResult = await conflictResolver.resolveConflictsBatch(conflicts, batchConfig);

        expect(batchResult).toBeDefined();
        expect(batchResult.totalConflicts).toBe(conflicts.length);
        expect(batchResult.resolvedConflicts).toBeGreaterThanOrEqual(0);
        expect(batchResult.resolutions.length).toBe(batchResult.resolvedConflicts);
        expect(batchResult.timing.durationMs).toBeGreaterThan(0);
        expect(batchResult.metadata.successRate).toBeGreaterThanOrEqual(0);
      }
    }, TEST_TIMEOUT);
  });

  // ============================================================================
  // PERFORMANCE AND MEMORY SAFETY TESTS
  // ============================================================================

  describe('Performance and Memory Safety', () => {
    test('should complete priority management within performance thresholds', async () => {
      const startTime = Date.now();

      await conflictResolver.manageRulePriorities(mockRules, TEST_PRIORITY_CONFIG);

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
    }, TEST_TIMEOUT);

    test('should handle large rule sets efficiently', async () => {
      // Create a larger rule set for performance testing
      const largeRuleSet = createMockRuleSet('large-ruleset',
        Array.from({ length: 50 }, (_, i) =>
          createMockGovernanceRule(`rule-${i}`, {
            priority: { level: Math.floor(Math.random() * 100) + 1, category: 'medium' }
          })
        )
      );

      const startTime = Date.now();

      const context: TConflictDetectionContext = {
        scope: 'ruleset',
        sensitivity: 'medium',
        filters: [],
        options: {
          includeWarnings: false,
          deepAnalysis: false,
          predictiveDetection: false,
          realTimeMonitoring: false
        }
      };

      const conflicts = await conflictResolver.detectConflicts(largeRuleSet, context);

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds for 50 rules
      expect(conflicts).toBeDefined();
    }, TEST_TIMEOUT);

    test('should not leak memory during repeated operations', async () => {
      const initialMetrics = await conflictResolver.getMetrics();

      // Perform multiple operations
      for (let i = 0; i < 10; i++) {
        await conflictResolver.manageRulePriorities(mockRules, TEST_PRIORITY_CONFIG);

        const context: TConflictDetectionContext = {
          scope: 'ruleset',
          sensitivity: 'low',
          filters: [],
          options: {
            includeWarnings: false,
            deepAnalysis: false,
            predictiveDetection: false,
            realTimeMonitoring: false
          }
        };

        await conflictResolver.detectConflicts(mockRuleSet, context);
      }

      const finalMetrics = await conflictResolver.getMetrics();

      // Memory usage should not grow excessively
      expect(finalMetrics.memoryUsage).toBeLessThan(0.9); // Should stay below 90%

      // Collections should not grow unbounded
      expect(finalMetrics.cachedConflicts).toBeLessThan(100);
    }, TEST_TIMEOUT);
  });

  // ============================================================================
  // ERROR HANDLING AND EDGE CASES TESTS
  // ============================================================================

  describe('Error Handling and Edge Cases', () => {
    test('should handle empty rule set gracefully', async () => {
      const emptyRuleSet = createMockRuleSet('empty-ruleset', []);

      const conflicts = await conflictResolver.detectPriorityConflicts(emptyRuleSet);
      expect(conflicts).toEqual([]);

      const validationResult = await conflictResolver.validatePriorityHierarchy(emptyRuleSet);
      expect(validationResult.valid).toBe(true);
      expect(validationResult.errors).toEqual([]);
    }, TEST_TIMEOUT);

    test('should handle invalid priority configuration', async () => {
      const invalidPriorityConfig: TRulePriorityConfig = {
        ...TEST_PRIORITY_CONFIG,
        maxPriorityLevels: -1 // Invalid value
      };

      // Should not throw error but may produce warnings
      const result = await conflictResolver.manageRulePriorities(mockRules, invalidPriorityConfig);
      expect(result).toBeDefined();
    }, TEST_TIMEOUT);

    test('should handle concurrent resolution attempts', async () => {
      const context: TConflictDetectionContext = {
        scope: 'ruleset',
        sensitivity: 'medium',
        filters: [],
        options: {
          includeWarnings: true,
          deepAnalysis: false,
          predictiveDetection: false,
          realTimeMonitoring: false
        }
      };

      const conflicts = await conflictResolver.detectConflicts(mockRuleSet, context);

      if (conflicts.length > 0) {
        const conflictId = conflicts[0].conflictId;

        // Attempt concurrent resolutions
        const resolutionPromises = [
          conflictResolver.resolveConflict(conflictId, 'priority-based'),
          conflictResolver.resolveConflict(conflictId, 'priority-based'),
          conflictResolver.resolveConflict(conflictId, 'priority-based')
        ];

        // At least one should succeed, others may fail gracefully
        const results = await Promise.allSettled(resolutionPromises);
        const successfulResults = results.filter(r => r.status === 'fulfilled');

        expect(successfulResults.length).toBeGreaterThan(0);
      }
    }, TEST_TIMEOUT);
  });

  // ============================================================================
  // INTEGRATION AND COMPLIANCE TESTS
  // ============================================================================

  describe('Integration and Compliance', () => {
    test('should comply with IGovernanceService interface', async () => {
      // Test all required interface methods
      expect(typeof conflictResolver.initialize).toBe('function');
      expect(typeof conflictResolver.validate).toBe('function');
      expect(typeof conflictResolver.getMetrics).toBe('function');
      expect(typeof conflictResolver.isReady).toBe('function');
      expect(typeof conflictResolver.shutdown).toBe('function');

      // Test interface method functionality
      const validationResult = await conflictResolver.validate();
      expect(validationResult).toBeDefined();
      expect(typeof validationResult.valid).toBe('boolean');

      const metrics = await conflictResolver.getMetrics();
      expect(metrics).toBeDefined();

      expect(typeof conflictResolver.isReady()).toBe('boolean');
    }, TEST_TIMEOUT);

    test('should integrate with BaseTrackingService correctly', async () => {
      // Test inherited functionality
      expect(conflictResolver.isHealthy()).toBe(true);
      expect(conflictResolver.isInitialized()).toBe(true);

      // Test resource metrics
      const resourceMetrics = conflictResolver.getResourceMetrics();
      expect(resourceMetrics).toBeDefined();
      expect(typeof resourceMetrics.memoryUsage).toBe('number');
      expect(typeof resourceMetrics.intervalCount).toBe('number');
      expect(typeof resourceMetrics.timeoutCount).toBe('number');
    }, TEST_TIMEOUT);

    test('should maintain resilient timing integration', async () => {
      // Test that resilient timer is properly initialized
      expect((conflictResolver as any)._resilientTimer).toBeDefined();
      expect((conflictResolver as any)._metricsCollector).toBeDefined();

      // Test timing-sensitive operations
      const startTime = Date.now();
      await conflictResolver.manageRulePriorities(mockRules, TEST_PRIORITY_CONFIG);
      const duration = Date.now() - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(10000); // 10 seconds max
    }, TEST_TIMEOUT);
  });
});
